"""
Configuration settings module.

This module provides backward compatibility by re-exporting constants
from the common.constants modules.
"""

# Re-export agents configuration
from .agents import AGENT_LLM_MAP, LLMType

# Re-export tools configuration  
from .tools import SELECTED_SEARCH_ENGINE, SearchEngine

# Re-export questions configuration
from .questions import BUILT_IN_QUESTIONS, BUILT_IN_QUESTIONS_ZH_CN

# Re-export MCP servers configuration
from .mcp_servers import get_mcp_settings, MCPServerConfig

__all__ = [
    # Agents
    'AGENT_LLM_MAP',
    'LLMType',
    
    # Tools
    'SELECTED_SEARCH_ENGINE', 
    'SearchEngine',
    
    # Questions
    'BUILT_IN_QUESTIONS',
    'BUILT_IN_QUESTIONS_ZH_CN',
    
    # MCP Servers
    'get_mcp_settings',
    'MCPServerConfig',
]
